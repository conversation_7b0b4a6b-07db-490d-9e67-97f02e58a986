package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpBenefitEntity;
import com.reinsurance.query.DwsPrpBenefitQuery;

/**
 * 再保生存金险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpBenefitMapper {
    
    /**
     * 查询再保生存金险种明细
     *
     * @param Id 再保生存金险种明细主键
     * @return 再保生存金险种明细
     */
    public DwsPrpBenefitEntity selectDwsPrpBenefitById(Long Id);

    /**
     * 查询再保生存金险种明细列表
     *
     * @param dwsPrpBenefitQuery 再保生存金险种明细
     * @return 再保生存金险种明细集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitList(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 根据主键数组查询再保生存金险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保生存金险种明细集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 新增再保生存金险种明细
     *
     * @param dwsPrpBenefitEntity 再保生存金险种明细
     * @return 结果
     */
    public int insertDwsPrpBenefit(DwsPrpBenefitEntity dwsPrpBenefitEntity);

    /**
     * 批量新增再保生存金险种明细
     *
     * @param dwsPrpBenefitList 再保生存金险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpBenefit(List<DwsPrpBenefitEntity> dwsPrpBenefitList);

    /**
     * 修改再保生存金险种明细
     *
     * @param dwsPrpBenefitEntity 再保生存金险种明细
     * @return 结果
     */
    public int updateDwsPrpBenefit(DwsPrpBenefitEntity dwsPrpBenefitEntity);

    /**
     * 删除再保生存金险种明细
     *
     * @param Id 再保生存金险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpBenefitById(Long Id);

    /**
     * 批量删除再保生存金险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 检查再保生存金险种明细是否存在
     *
     * @param dwsPrpBenefitQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpBenefitExists(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpBenefitPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);
}
