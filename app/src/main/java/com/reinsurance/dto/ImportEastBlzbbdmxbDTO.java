package com.reinsurance.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.reinsurance.core.convert.EastEitherConvert;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导入East比例再保保单明细对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper=true)
public class ImportEastBlzbbdmxbDTO extends BaseDTO {
    
	private static final long serialVersionUID = -3138938315558370607L;

	/** 账单流水号 */
    private String billLsh;

    /** 流水号 */
    private String lsh;

    /** 保险机构代码（唯一固定值：000166） */
    @ExcelProperty(value = "保险机构代码")
    private String bxjgdm;

    /** 保险机构名称（唯一固定值：弘康人寿保险股份有限公司） */
    @ExcelProperty(value = "保险机构名称")
    private String bxjgmc;

    /** 临分保单标志（0=否,1=是） */
    @ExcelProperty(value = "临分保单标志", converter = EastEitherConvert.class)
    private String lfbdbz;

    /** 虚拟合同标志（0=否,1=是） */
    @ExcelProperty(value = "虚拟合同标志", converter = EastEitherConvert.class)
    private String xnhtbz;

    /** 再保险合同号码 */
    @ExcelProperty(value = "再保险合同号码")
    private String zbxhthm;

    /** 再保险公司代码 */
    @ExcelProperty(value = "再保险公司代码")
    private String zbxgsdm;

    /** 再保险公司名称 */
    @ExcelProperty(value = "再保险公司名称")
    private String zbxgsmc;

    /** 政保合作业务标志（0=否,1=是） */
    @ExcelProperty(value = "政保合作业务标志", converter = EastEitherConvert.class)
    private String zbhzywbz;

    /** 保单团个性质（个人,团体） */
    @ExcelProperty(value = "保单团个性质")
    private String bdtgxz;

    /** 团体保单号 */
    @ExcelProperty(value = "团体保单号")
    private String ttbdh;

    /** 团单保险险种号码 */
    @ExcelProperty(value = "团单保险险种号码")
    private String tdbxxzhm;

    /** 个人保单号 */
    @ExcelProperty(value = "个人保单号")
    private String grbdh;

    /** 个单保险险种号码 */
    @ExcelProperty(value = "个单保险险种号码")
    private String gdbxxzhm;

    /** 险种编码 */
    @ExcelProperty(value = "险种编码")
    private String xzbm;

    /** 责任代码 */
    @ExcelProperty(value = "责任代码")
    private String zrdm;

    /** 责任名称 */
    @ExcelProperty(value = "责任名称")
    private String zrmc;

    /** 责任分类 */
    @ExcelProperty(value = "责任分类")
    private String zrfl;

    /** 业务类型(新契约；续期；保全；理赔；其他) */
    @ExcelProperty(value = "业务类型")
    private String ywlx;

    /** 保单年度 */
    @ExcelProperty(value = "保单年度")
    private Integer bdnd;

    /** 分保方式（枚举值：溢额（YRT）；成数（YRT）；成数溢额混合（YRT）；共保；超赔；超额赔付率） */
    @ExcelProperty(value = "分保方式")
    private String fbfs;

    /** 风险保额 */
    @ExcelProperty(value = "风险保额")
    private String fxbe;

    /** 分保风险保额 */
    @ExcelProperty(value = "分保风险保额")
    private String fbfxbe;

    /** 自留额 */
    @ExcelProperty(value = "自留额")
    private String zle;

    /** 分保比例 */
    @ExcelProperty(value = "分保比例")
    private String fbbl;

    /** 再保人参与份额比例(接受份额*100) */
    @ExcelProperty(value = "再保人参与份额比例")
    private String zbrcyfebl;

    /** 分保或摊回计算编号 */
    @ExcelProperty(value = "分保或摊回计算编号")
    private String fbhthjsbh;

    /** 分保费 */
    @ExcelProperty(value = "分保费")
    private BigDecimal fbf;

    /** 分保佣金 */
    @ExcelProperty(value = "分保佣金")
    private BigDecimal fbyj;

    /** 退回分保费 */
    @ExcelProperty(value = "退回分保费")
    private BigDecimal thfbf;

    /** 退回分保佣金 */
    @ExcelProperty(value = "退回分保佣金")
    private BigDecimal thfbyj;

    /** 摊回退保金 */
    @ExcelProperty(value = "摊回退保金")
    private BigDecimal thtbj;

    /** 摊回理赔款 */
    @ExcelProperty(value = "摊回理赔款")
    private BigDecimal thlpk;

    /** 赔案号 */
    @ExcelProperty(value = "赔案号")
    private String pah;

    /** 摊回满期金 */
    @ExcelProperty(value = "摊回满期金")
    private BigDecimal thmqj;

    /** 摊回生存金 */
    @ExcelProperty(value = "摊回生存金")
    private BigDecimal thscj;

    /** 计算日期 */
    @ExcelProperty(value = "计算日期")
    private String jsrq;

    /** 货币代码 */
    @ExcelProperty(value = "货币代码")
    private String hbdm;

    /** 保单状态 */
    @ExcelProperty(value = "保单状态")
    private String bdzt;

    /** 备注 */
    @ExcelProperty(value = "备注")
    private String bz;

    /** 采集日期 */
    private String cjrq;
    
    /**数据报送批次号*/
    private String sjbspch;
    
    /**管理机构*/
    private String managecom;
    
    /** 数据来源（0=系统,1=人工） */
    private Integer dataSource = ReportDataSource.人工.getCode();
    
    /** 推送状态（0=未推送,1=已推送） */
    private Integer pushStatus = ReportPushStatus.未推送.getCode();

    /** 推送日期 */
    private Date pushDate;

    /** 推送人 */
    private String pushBy;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel = CedeoutEnums.未删除.getValue();
}
