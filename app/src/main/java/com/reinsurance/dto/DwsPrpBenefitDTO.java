package com.reinsurance.dto;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.annotation.Excel.Type;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 再保生存金险种明细对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpBenefitDTO extends PrpBaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    @Excel(name = "交易编码", type=Type.EXPORT)
    @NotBlank(message = "交易编码不能为空")
    @Size(max = 64, message = "交易编码长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @Excel(name = "保险机构代码")
    @NotBlank(message = "保险机构代码不能为空")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 团体保单号 */
    @JsonProperty("GrpPolicyNo")
    @Excel(name = "团体保单号")
    @Size(max = 64, message = "团体保单号长度不能超过64个字符")
    private String GrpPolicyNo;

    /** 团单保险险种号码 */
    @JsonProperty("GrpProductNo")
    @Excel(name = "团单保险险种号码")
    @Size(max = 64, message = "团单保险险种号码长度不能超过64个字符")
    private String GrpProductNo;

    /** 个人保单号 */
    @JsonProperty("PolicyNo")
    @Excel(name = "个人保单号")
    @NotBlank(message = "个人保单号不能为空")
    @Size(max = 64, message = "个人保单号长度不能超过64个字符")
    private String PolicyNo;

    /** 个单保险险种号码 */
    @JsonProperty("ProductNo")
    @Excel(name = "个单保险险种号码")
    @NotBlank(message = "个单保险险种号码不能为空")
    @Size(max = 64, message = "个单保险险种号码长度不能超过64个字符")
    private String ProductNo;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    @Excel(name = "团个性质", dictType = "prp_gp_flag")
    @NotBlank(message = "团个性质不能为空")
    @Size(max = 4, message = "团个性质长度不能超过4个字符")
    private String GPFlag;

    /** 主险保险险种号码 */
    @JsonProperty("MainProductNo")
    @Excel(name = "主险保险险种号码")
    @NotBlank(message = "主险保险险种号码不能为空")
    @Size(max = 64, message = "主险保险险种号码长度不能超过64个字符")
    private String MainProductNo;

    /** 主附险性质代码（1=主险,2=附加险,3=不区分） */
    @JsonProperty("MainProductFlag")
    @Excel(name = "主附险性质代码", dictType = "prp_main_product_flag")
    @NotBlank(message = "主附险性质代码不能为空")
    @Size(max = 4, message = "主附险性质代码长度不能超过4个字符")
    private String MainProductFlag;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    @Excel(name = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @Size(max = 64, message = "产品编码长度不能超过64个字符")
    private String ProductCode;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    @Excel(name = "责任代码")
    @NotBlank(message = "责任代码不能为空")
    @Size(max = 64, message = "责任代码长度不能超过64个字符")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    @Excel(name = "责任名称")
    @NotBlank(message = "责任名称不能为空")
    @Size(max = 128, message = "责任名称长度不能超过128个字符")
    private String LiabilityName;

    /** 责任分类代码 */
    @JsonProperty("Classification")
    @Excel(name = "责任分类代码")
    @Size(max = 64, message = "责任分类代码长度不能超过64个字符")
    private String Classification;

    /** 业务类型(01=新单,02=续期,03=续保) */
    @JsonProperty("EventType")
    @Excel(name = "业务类型", dictType = "prp_event_type")
    @NotBlank(message = "业务类型不能为空")
    @Size(max = 4, message = "业务类型长度不能超过4个字符")
    private String EventType;

    /** 保单年度 */
    @JsonProperty("PolYear")
    @Excel(name = "保单年度")
    @NotNull(message = "保单年度不能为空")
    @Min(value = 1, message = "保单年度最小值为1")
    private Integer PolYear;

    /** 续期续保次数(保单年度-1) */
    @JsonProperty("RenewalTimes")
    @Excel(name = "续期续保次数")
    @Min(value = 0, message = "续期续保次数最小值为0")
    private Integer RenewalTimes;

    /** 保险期限类型 */
    @JsonProperty("TermType")
    @Excel(name = "保险期限类型")
    @Size(max = 4, message = "保险期限类型长度不能超过4个字符")
    private String TermType;

    /** 管理机构代码 */
    @JsonProperty("ManageCom")
    @Excel(name = "管理机构代码")
    @Size(max = 4, message = "管理机构代码长度不能超过4个字符")
    private String ManageCom;

    /** 签单日期 */
    @JsonProperty("SignDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签单日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "签单日期不能为空")
    private Date SignDate;

    /** 保险责任生效日期 */
    @JsonProperty("EffDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保险责任生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "保险责任生效日期不能为空")
    private Date EffDate;

    /** 保险责任终止日期 */
    @JsonProperty("InvalidDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保险责任终止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "保险责任终止日期不能为空")
    private Date InvalidDate;

    /** 核保结论代码 */
    @JsonProperty("UWConclusion")
    @Excel(name = "核保结论代码")
    @Size(max = 64, message = "核保结论代码长度不能超过64个字符")
    private String UWConclusion;

    /** 保单状态代码 */
    @JsonProperty("PolStatus")
    @Excel(name = "保单状态代码", dictType = "prp_cont_pol_duty_status")
    @NotBlank(message = "保单状态代码不能为空")
    @Size(max = 4, message = "保单状态代码长度不能超过4个字符")
    private String PolStatus;

    /** 保单险种状态代码 */
    @JsonProperty("Status")
    @Excel(name = "保单险种状态代码", dictType = "prp_cont_pol_duty_status")
    @NotBlank(message = "保单险种状态代码不能为空")
    @Size(max = 4, message = "保单险种状态代码长度不能超过4个字符")
    private String Status;

    /** 基本保额 */
    @JsonProperty("BasicSumInsured")
    @Excel(name = "基本保额")
    @DecimalMin(value = "0", message = "基本保额不能小于0")
    private BigDecimal BasicSumInsured;

    /** 风险保额 */
    @JsonProperty("RiskAmnt")
    @Excel(name = "风险保额")
    @DecimalMin(value = "0", message = "风险保额不能小于0")
    private BigDecimal RiskAmnt;

    /** 保费 */
    @JsonProperty("Premium")
    @Excel(name = "保费")
    @DecimalMin(value = "0", message = "保费不能小于0")
    private BigDecimal Premium;

    /** 保险账户价值 */
    @JsonProperty("AccountValue")
    @Excel(name = "保险账户价值")
    @DecimalMin(value = "0", message = "保险账户价值不能小于0")
    private BigDecimal AccountValue;

    /** 临分标记（0=否,1=是） */
    @JsonProperty("FacultativeFlag")
    @Excel(name = "临分标记")
    @NotBlank(message = "临分标记不能为空")
    @Size(max = 64, message = "临分标记长度不能超过64个字符")
    private String FacultativeFlag;

    /** 无名单标志 */
    @JsonProperty("AnonymousFlag")
    @Excel(name = "无名单标志")
    @Size(max = 4, message = "无名单标志长度不能超过4个字符")
    private String AnonymousFlag;

    /** 豁免险标志（0=否,1=是） */
    @JsonProperty("WaiverFlag")
    @Excel(name = "豁免险标志")
    @Size(max = 4, message = "豁免险标志长度不能超过4个字符")
    private String WaiverFlag;

    /** 所需豁免剩余保费 */
    @JsonProperty("WaiverPrem")
    @Excel(name = "所需豁免剩余保费")
    @DecimalMin(value = "0", message = "所需豁免剩余保费不能小于0")
    private BigDecimal WaiverPrem;

    /** 期末现金价值 */
    @JsonProperty("FinalCashValue")
    @Excel(name = "期末现金价值")
    @DecimalMin(value = "0", message = "期末现金价值不能小于0")
    private BigDecimal FinalCashValue;

    /** 期末责任准备金 */
    @JsonProperty("FinalLiabilityReserve")
    @Excel(name = "期末责任准备金")
    @DecimalMin(value = "0", message = "期末责任准备金不能小于0")
    private BigDecimal FinalLiabilityReserve;

    /** 被保人客户号 */
    @JsonProperty("InsuredNo")
    @Excel(name = "被保人客户号")
    @NotBlank(message = "被保人客户号不能为空")
    @Size(max = 64, message = "被保人客户号长度不能超过64个字符")
    private String InsuredNo;

    /** 被保人姓名 */
    @JsonProperty("InsuredName")
    @Excel(name = "被保人姓名")
    @NotBlank(message = "被保人姓名不能为空")
    @Size(max = 64, message = "被保人姓名长度不能超过64个字符")
    private String InsuredName;

    /** 被保人性别 */
    @JsonProperty("InsuredSex")
    @Excel(name = "被保人性别", dictType = "sys_user_sex")
    @NotBlank(message = "被保人性别不能为空")
    @Size(max = 4, message = "被保人性别长度不能超过4个字符")
    private String InsuredSex;

    /** 被保人证件类型 */
    @JsonProperty("InsuredCertType")
    @Excel(name = "被保人证件类型", dictType = "prp_cert_type")
    @NotBlank(message = "被保人证件类型不能为空")
    @Size(max = 4, message = "被保人证件类型长度不能超过4个字符")
    private String InsuredCertType;

    /** 被保人证件编码 */
    @JsonProperty("InsuredCertNo")
    @Excel(name = "被保人证件编码")
    @NotBlank(message = "被保人证件编码不能为空")
    @Size(max = 64, message = "被保人证件编码长度不能超过64个字符")
    private String InsuredCertNo;

    /** 职业代码 */
    @JsonProperty("OccupationType")
    @Excel(name = "职业代码")
    @NotBlank(message = "职业代码不能为空")
    @Size(max = 12, message = "职业代码长度不能超过12个字符")
    private String OccupationType;

    /** 投保年龄 */
    @JsonProperty("AppntAge")
    @Excel(name = "投保年龄")
    @Min(value = 0, message = "投保年龄最小值为0")
    @Max(value = 150, message = "投保年龄最大值为150")
    private Integer AppntAge;

    /** 当前年龄 */
    @JsonProperty("PreAge")
    @Excel(name = "当前年龄")
    @Min(value = 0, message = "当前年龄最小值为0")
    @Max(value = 150, message = "当前年龄最大值为150")
    private Integer PreAge;

    /** 职业加费金额 */
    @JsonProperty("ProfessionalFee")
    @Excel(name = "职业加费金额")
    @DecimalMin(value = "0", message = "职业加费金额不能小于0")
    private BigDecimal ProfessionalFee;

    /** 次标准体加费金额 */
    @JsonProperty("SubStandardFee")
    @Excel(name = "次标准体加费金额")
    @DecimalMin(value = "0", message = "次标准体加费金额不能小于0")
    private BigDecimal SubStandardFee;

    /** EM加点 */
    @JsonProperty("EMRate")
    @Excel(name = "EM加点")
    @DecimalMin(value = "0", message = "EM加点不能小于0")
    private BigDecimal EMRate;

    /** 建工险标志 */
    @JsonProperty("ProjectFlag")
    @Excel(name = "建工险标志")
    @Size(max = 4, message = "建工险标志长度不能超过4个字符")
    private String ProjectFlag;

    /** 被保人数 */
    @JsonProperty("InsurePeoples")
    @Excel(name = "被保人数")
    @Min(value = 1, message = "被保人数最小值为1")
    private Integer InsurePeoples;

    /** 分出标记（0=未达到溢额线保单,1=分出保单） */
    @JsonProperty("SaparateFlag")
    @Excel(name = "分出标记")
    @NotBlank(message = "分出标记不能为空")
    @Size(max = 4, message = "分出标记长度不能超过4个字符")
    private String SaparateFlag;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @Excel(name = "再保险合同号码")
    @NotBlank(message = "再保险合同号码不能为空")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @Excel(name = "再保险公司代码")
    @NotBlank(message = "再保险公司代码不能为空")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @Excel(name = "再保险公司名称")
    @NotBlank(message = "再保险公司名称不能为空")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    @Excel(name = "分保方式", dictType = "prp_reinsur_mode")
    @NotBlank(message = "分保方式不能为空")
    @Size(max = 4, message = "分保方式长度不能超过4个字符")
    private String ReinsurMode;

    /** 生存金号 */
    @JsonProperty("BenefitNo")
    @Excel(name = "生存金号")
    @NotBlank(message = "生存金号不能为空")
    @Size(max = 64, message = "生存金号长度不能超过64个字符")
    private String BenefitNo;

    /** 生存金类型 */
    @JsonProperty("BenefitType")
    @Excel(name = "生存金类型")
    @NotBlank(message = "生存金类型不能为空")
    @Size(max = 4, message = "生存金类型长度不能超过4个字符")
    private String BenefitType;

    /** 生存金给付日期 */
    @JsonProperty("BenefitDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生存金给付日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "生存金给付日期不能为空")
    private Date BenefitDate;

    /** 生存金给付原因 */
    @JsonProperty("BenefitReason")
    @Excel(name = "生存金给付原因")
    @NotBlank(message = "生存金给付原因不能为空")
    @Size(max = 4, message = "生存金给付原因长度不能超过4个字符")
    private String BenefitReason;

    /** 生存金给付金额 */
    @JsonProperty("BenefitAmount")
    @Excel(name = "生存金给付金额")
    @NotNull(message = "生存金给付金额不能为空")
    @DecimalMin(value = "0", message = "生存金给付金额不能小于0")
    private BigDecimal BenefitAmount;

    /** 自留额 */
    @JsonProperty("RetentionAmount")
    @Excel(name = "自留额")
    @DecimalMin(value = "0", message = "自留额不能小于0")
    private BigDecimal RetentionAmount;

    /** 分保生存金金额 */
    @JsonProperty("ReinsuranceBenefitAmount")
    @Excel(name = "分保生存金金额")
    @DecimalMin(value = "0", message = "分保生存金金额不能小于0")
    private BigDecimal ReinsuranceBenefitAmount;

    /** 货币代码 */
    @JsonProperty("Currency")
    @Excel(name = "货币代码")
    @NotBlank(message = "货币代码不能为空")
    @Size(max = 4, message = "货币代码长度不能超过4个字符")
    private String Currency;

    /** 分保计算日期 */
    @JsonProperty("ReComputationsDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分保计算日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ReComputationsDate;

    /** 账单归属日期 */
    @JsonProperty("AccountGetDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单归属日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date AccountGetDate;

    /** 所属账单流水号 */
    @JsonProperty("AccTransNo")
    @Excel(name = "所属账单流水号")
    @NotBlank(message = "所属账单流水号不能为空")
    @Size(max = 64, message = "所属账单流水号长度不能超过64个字符")
    private String AccTransNo;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    @Excel(name = "数据来源", type=Type.EXPORT, dictType = "regulator_report_data_source")
    @NotNull(message = "数据来源不能为空")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    @Excel(name = "推送状态", type=Type.EXPORT, dictType = "regulator_report_push_status")
    @NotNull(message = "推送状态不能为空")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送日期", type=Type.EXPORT, width = 30, dateFormat = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    @Excel(name = "推送人", type=Type.EXPORT)
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    @Excel(name = "备注")
    @Size(max = 128, message = "备注长度不能超过128个字符")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;
}
