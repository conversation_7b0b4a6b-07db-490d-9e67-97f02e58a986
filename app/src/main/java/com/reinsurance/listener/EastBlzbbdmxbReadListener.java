package com.reinsurance.listener;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.StarRocksConnector;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.domain.DwsEastZbzdxxbEntity;
import com.reinsurance.dto.DwsEastZbzdxxbDTO;
import com.reinsurance.dto.ImportEastBlzbbdmxbDTO;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.service.IDwsEastZbzdxxbService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * East比例再保保单明细导入处理类
 * <AUTHOR>
 *
 */
@Slf4j
public class EastBlzbbdmxbReadListener implements ReadListener<ImportEastBlzbbdmxbDTO> {
	
	private String executor;
	
	private Date importDate;
	
	private long insertRows = 0;
	
	private DwsEastZbzdxxbEntity zbzdxxb;
	
	private IRedisService redisService;
	
	private StarRocksConnector starRocksConnector;
	
	private IDwsEastZbzdxxbService dwsEastZbzdxxbService;
	
	private static final int BATCH_SAVE_COUNT = 10000;
	
    private List<ImportEastBlzbbdmxbDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_SAVE_COUNT);
	
    public EastBlzbbdmxbReadListener(DwsEastZbzdxxbEntity zbzdxxb, IRedisService redisService, StarRocksConnector starRocksConnector) {
        this.zbzdxxb = zbzdxxb;
        this.importDate = new Date();
        this.redisService = redisService;
        this.starRocksConnector = starRocksConnector;
        this.dwsEastZbzdxxbService = SpringUtils.getBean(IDwsEastZbzdxxbService.class);
    }
    
	@Override
	public void invoke(ImportEastBlzbbdmxbDTO blzbbdmxb, AnalysisContext context) {
		blzbbdmxb.setCreateBy(executor);
		blzbbdmxb.setUpdateBy(executor);
		blzbbdmxb.setCreateTime(importDate);
		blzbbdmxb.setUpdateTime(importDate);
		blzbbdmxb.setCjrq(zbzdxxb.getCjrq());
		blzbbdmxb.setBillLsh(zbzdxxb.getLsh());
		blzbbdmxb.setSjbspch(zbzdxxb.getSjbspch());
		blzbbdmxb.setManagecom(zbzdxxb.getManagecom());
		//风险保额
		BigDecimal fxbe = new BigDecimal(blzbbdmxb.getFxbe());
		if(BigDecimal.ZERO.compareTo(fxbe) == 0) {//表示是0
			blzbbdmxb.setFxbe(String.valueOf(BigDecimal.ZERO));
		}else {
			blzbbdmxb.setFxbe(String.format("%.4f", fxbe));
		}
		//分保风险保额
		BigDecimal fbfxbe = new BigDecimal(blzbbdmxb.getFbfxbe());
		if(BigDecimal.ZERO.compareTo(fxbe) == 0) {//表示是0
			blzbbdmxb.setFbfxbe(String.valueOf(BigDecimal.ZERO));
		}else {
			blzbbdmxb.setFbfxbe(String.format("%.4f", fbfxbe));
		}
		//自留额
		BigDecimal zle = new BigDecimal(blzbbdmxb.getZle());
		if(BigDecimal.ZERO.compareTo(zle) == 0) {//表示是0
			blzbbdmxb.setZle(String.valueOf(BigDecimal.ZERO));
		}else {
			blzbbdmxb.setZle(String.format("%.4f", zle));
		}
		//分保比例
		BigDecimal fbbl = new BigDecimal(blzbbdmxb.getFbbl());
		if(BigDecimal.ONE.negate().compareTo(fbbl) == 0) {//表示是-1
			blzbbdmxb.setFbbl(String.valueOf(BigDecimal.ONE.negate()));
		}else {
			blzbbdmxb.setFbbl(String.format("%.2f", fbbl));
		}
		//再保人参与份额比例
		blzbbdmxb.setZbrcyfebl(String.format("%.2f", Float.valueOf(blzbbdmxb.getZbrcyfebl())));
		cachedDataList.add(blzbbdmxb);
		if (cachedDataList.size() >= BATCH_SAVE_COUNT) {
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, zbzdxxb.getBxjgdm(), cachedDataList.size());
			if(CollUtil.isEmpty(serialNos) || cachedDataList.size() != serialNos.size()) {
				log.info("再保导入East比例再保保单明细生成流水号失败, zbzdxxb:{}", ReinsuJsonUtil.toJsonString(zbzdxxb));
			}else {
				int index = 0;
				for(ImportEastBlzbbdmxbDTO item : cachedDataList) {
					item.setLsh(serialNos.get(index));
					index++;
				}
				try {
					StarRocksOperationResult starRocksResult = starRocksConnector.insert("T_DWS_EAST_BLZBBDMXB", ReinsuJsonUtil.toUpperCaseJsonString(cachedDataList));
					String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
					if(!("success").equalsIgnoreCase(status)) {//操作失败
						log.info("再保导入East比例再保保单明细失败, zbzdxxb:{}, starRocksResult:{}", ReinsuJsonUtil.toJsonString(zbzdxxb), ReinsuJsonUtil.toJsonString(starRocksResult));
					}else {
						insertRows += starRocksResult.getNumberTotalRows();
						log.info("再保导入East比例再保保单明细处理中, insertRows:{}", insertRows);
					}
				}catch(Exception e) {
					log.error("再保导入East比例再保保单明细失败, zbzdxxb:{}, 失败原因: ", ReinsuJsonUtil.toJsonString(zbzdxxb), e);
				}
			}
			cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_SAVE_COUNT);
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		if(cachedDataList.size() <= 0) {
			log.info("再保导入East比例再保保单明细结束, 未解析到数据");
			return;
		}
		try {
			int totalRows = context.readRowHolder().getRowIndex();
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, zbzdxxb.getBxjgdm(), cachedDataList.size());
			if(CollUtil.isEmpty(serialNos) || cachedDataList.size() != serialNos.size()) {
				log.info("再保导入East比例再保保单明细生成流水号失败, zbzdxxb:{}", ReinsuJsonUtil.toJsonString(zbzdxxb));
				return;
			}
			int index = 0;
			for(ImportEastBlzbbdmxbDTO item : cachedDataList) {
				item.setLsh(serialNos.get(index));
				index++;
			}
			StarRocksOperationResult starRocksResult = starRocksConnector.insert("T_DWS_EAST_BLZBBDMXB", ReinsuJsonUtil.toUpperCaseJsonString(cachedDataList));
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.info("再保导入East比例再保保单明细失败, zbzdxxb:{}, starRocksResult:{}", ReinsuJsonUtil.toJsonString(zbzdxxb), ReinsuJsonUtil.toJsonString(starRocksResult));
				return;
			}
			insertRows += starRocksResult.getNumberTotalRows();
			
			zbzdxxb.setUpdateBy(executor);
			zbzdxxb.setUpdateTime(DateUtils.getNowDate());
			zbzdxxb.setImportStatus(CedeoutEnums.导入状态_已导入.getValue());
			zbzdxxb.setRemark("totalRows:" + totalRows + ";insertRows:" + insertRows);
			DwsEastZbzdxxbDTO dwsEastZbzdxxbDTO = ReinsuObjectUtil.convertModel(zbzdxxb, DwsEastZbzdxxbDTO.class);
			int zbzdxxbRows = dwsEastZbzdxxbService.updateZbzdxxbImportStatusByLsh(dwsEastZbzdxxbDTO);
			log.info("再保导入East比例再保保单明细完成, totalRows:{}, insertRows:{}, zbzdxxbRows:{}", totalRows, insertRows, zbzdxxbRows);
		}catch(Exception e) {
			log.error("再保导入East比例再保保单明细失败, zbzdxxb:{}, 失败原因: ", ReinsuJsonUtil.toJsonString(zbzdxxb), e);
		}
	}

}
