package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpBenefitDTO;
import com.reinsurance.query.DwsPrpBenefitQuery;
import com.reinsurance.service.IDwsPrpBenefitService;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保生存金险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/benefit")
public class DwsPrpBenefitController extends BaseController {
    
    @Autowired
    private IDwsPrpBenefitService dwsPrpBenefitService;

    /**
     * 查询再保生存金险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        startPage();
        List<DwsPrpBenefitDTO> list = dwsPrpBenefitService.selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保生存金险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:export')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        dwsPrpBenefitService.exportDwsPrpBenefit(response, dwsPrpBenefitQuery);
    }

    /**
     * 获取再保生存金险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpBenefitService.selectDwsPrpBenefitById(Id));
    }

    /**
     * 新增再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:add')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        return toAjax(dwsPrpBenefitService.insertDwsPrpBenefit(dwsPrpBenefitDTO));
    }

    /**
     * 修改再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:edit')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        return toAjax(dwsPrpBenefitService.updateDwsPrpBenefit(dwsPrpBenefitDTO));
    }

    /**
     * 删除再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:remove')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpBenefitService.deleteDwsPrpBenefitByIds(Ids));
    }

    /**
     * 导入再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:import')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpBenefitService.importDwsPrpBenefit(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpBenefitDTO> util = new ExcelUtil<>(DwsPrpBenefitDTO.class);
        util.importTemplateExcel(response, "再保生存金险种明细数据");
    }

    /**
     * 推送再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:push')")
    @Log(title = "推送再保生存金险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody Long[] Ids) {
        return dwsPrpBenefitService.updateDwsPrpBenefitPushStatus(Ids);
    }

    /**
     * 检查再保生存金险种明细是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        int count = dwsPrpBenefitService.selectDwsPrpBenefitExists(dwsPrpBenefitQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保生存金险种明细数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:generate')")
    @Log(title = "生成再保生存金险种明细数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpBenefitService.generatePrpBenefitData(startDate, endDate, reportYear, reportMonth);
    }
}
