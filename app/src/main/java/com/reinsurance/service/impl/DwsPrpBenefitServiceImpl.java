package com.reinsurance.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.constant.HttpStatus;

import com.reinsurance.mapper.DwsPrpBenefitMapper;
import com.reinsurance.dto.DwsPrpBenefitDTO;
import com.reinsurance.query.DwsPrpBenefitQuery;
import com.reinsurance.domain.DwsPrpBenefitEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpBenefitService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.ReportDataSource;
import com.reinsurance.enums.ReportPushStatus;
import com.reinsurance.enums.RegulatorReport;
import com.reinsurance.enums.PrpReport;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保生存金险种明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpBenefitServiceImpl implements IDwsPrpBenefitService {
    
    @Autowired
    private DwsPrpBenefitMapper dwsPrpBenefitMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询再保生存金险种明细
     *
     * @param Id 再保生存金险种明细主键
     * @return 再保生存金险种明细
     */
    @Override
    public DwsPrpBenefitDTO selectDwsPrpBenefitById(Long Id) {
        DwsPrpBenefitEntity entity = dwsPrpBenefitMapper.selectDwsPrpBenefitById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpBenefitDTO.class);
    }

    /**
     * 查询再保生存金险种明细列表
     *
     * @param dwsPrpBenefitQuery 再保生存金险种明细
     * @return 再保生存金险种明细
     */
    @Override
    public List<DwsPrpBenefitDTO> selectDwsPrpBenefitList(DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        List<DwsPrpBenefitEntity> entityList = dwsPrpBenefitMapper.selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpBenefitDTO.class);
    }

    /**
     * 新增再保生存金险种明细
     *
     * @param dwsPrpBenefitDTO 再保生存金险种明细
     * @return 结果
     */
    @Override
    public int insertDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        DwsPrpBenefitEntity dwsPrpBenefitEntity = ReinsuObjectUtil.convertModel(dwsPrpBenefitDTO, DwsPrpBenefitEntity.class);
        dwsPrpBenefitEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpBenefitMapper.insertDwsPrpBenefit(dwsPrpBenefitEntity);
    }

    /**
     * 批量新增再保生存金险种明细
     *
     * @param dwsPrpBenefitList 再保生存金险种明细列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpBenefit(List<DwsPrpBenefitDTO> dwsPrpBenefitList) {
        List<DwsPrpBenefitEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpBenefitList, DwsPrpBenefitEntity.class);
        return dwsPrpBenefitMapper.insertBatchDwsPrpBenefit(entitys);
    }

    /**
     * 修改再保生存金险种明细
     *
     * @param dwsPrpBenefitDTO 再保生存金险种明细
     * @return 结果
     */
    @Override
    public int updateDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        DwsPrpBenefitEntity dwsPrpBenefitEntity = ReinsuObjectUtil.convertModel(dwsPrpBenefitDTO, DwsPrpBenefitEntity.class);
        dwsPrpBenefitEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpBenefitMapper.updateDwsPrpBenefit(dwsPrpBenefitEntity);
    }

    /**
     * 批量删除再保生存金险种明细
     *
     * @param Ids 需要删除的再保生存金险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpBenefitByIds(Long[] Ids) {
        return dwsPrpBenefitMapper.deleteDwsPrpBenefitByIds(Ids);
    }

    /**
     * 删除再保生存金险种明细信息
     *
     * @param Id 再保生存金险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpBenefitById(Long Id) {
        DwsPrpBenefitEntity entity = dwsPrpBenefitMapper.selectDwsPrpBenefitById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpBenefitMapper.deleteDwsPrpBenefitById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRBenefit.getCode(), Arrays.asList(entity.getPolYear()));
        }
        return deleteRows;
    }

    /**
     * 导入再保生存金险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpBenefit(String companyCode, String companyName, String manageCom, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpBenefitDTO> util = new ExcelUtil<>(DwsPrpBenefitDTO.class);
            List<DwsPrpBenefitDTO> benefitList = util.importExcel(file.getInputStream());
            
            if (StringUtils.isNull(benefitList) || benefitList.size() == 0) {
                return Result.error("导入数据不能为空！");
            }
            
            // 2. 数据校验和处理
            for (DwsPrpBenefitDTO dto : benefitList) {
                dto.setCompanyCode(companyCode);
                dto.setDataSource(ReportDataSource.人工.getCode());
                dto.setPushStatus(ReportPushStatus.未推送.getCode());
                dto.setCreateBy(SecurityUtils.getUsername());
                dto.setCreateTime(DateUtils.getNowDate());
            }
            
            // 3. 批量入库
            List<DwsPrpBenefitEntity> dwsPrpBenefitList = ReinsuObjectUtil.convertList(benefitList, DwsPrpBenefitEntity.class);
            int insertRows = dwsPrpBenefitMapper.insertBatchDwsPrpBenefit(dwsPrpBenefitList);
            if(insertRows > 0) {
                // 4. 更新监管报表推送状态
                List<Integer> reportYears = benefitList.stream().map(DwsPrpBenefitDTO::getPolYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRBenefit.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入生存金险种明细出错, 错误原因:", e);
            return Result.error("导入生存金险种明细表出错，请联系管理员。");
        }
    }

    /**
     * 导出再保生存金险种明细
     *
     * @param response 响应对象
     * @param dwsPrpBenefitQuery 查询条件
     */
    @Override
    public void exportDwsPrpBenefit(HttpServletResponse response, DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        List<DwsPrpBenefitDTO> list = selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        ExcelUtil<DwsPrpBenefitDTO> util = new ExcelUtil<>(DwsPrpBenefitDTO.class);
        util.exportExcel(response, list, "再保生存金险种明细数据");
    }

    /**
     * 检查再保生存金险种明细是否存在
     *
     * @param dwsPrpBenefitQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpBenefitExists(DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        return dwsPrpBenefitMapper.selectDwsPrpBenefitExists(dwsPrpBenefitQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpBenefitPushStatus(Long[] Ids) {
        String pushBy = SecurityUtils.getUsername();
        int updateRows = dwsPrpBenefitMapper.updateDwsPrpBenefitPushStatus(ReportPushStatus.已推送.getCode(), pushBy, Ids);
        if(updateRows > 0) {
            return Result.success("推送成功");
        }
        return Result.error("推送失败");
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpBenefitMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    /**
     * 生成再保生存金险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    @Override
    public Result generatePrpBenefitData(String startDate, String endDate, Integer reportYear, Integer reportMonth) {
        // TODO: 实现数据生成逻辑
        return Result.success("数据生成功能待实现");
    }
}
