package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpBenefitDTO;
import com.reinsurance.query.DwsPrpBenefitQuery;

/**
 * 再保生存金险种明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDwsPrpBenefitService {
    
    /**
     * 查询再保生存金险种明细
     *
     * @param Id 再保生存金险种明细主键
     * @return 再保生存金险种明细
     */
    public DwsPrpBenefitDTO selectDwsPrpBenefitById(Long Id);

    /**
     * 查询再保生存金险种明细列表
     *
     * @param dwsPrpBenefitQuery 再保生存金险种明细
     * @return 再保生存金险种明细集合
     */
    public List<DwsPrpBenefitDTO> selectDwsPrpBenefitList(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 新增再保生存金险种明细
     *
     * @param dwsPrpBenefitDTO 再保生存金险种明细
     * @return 结果
     */
    public int insertDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO);

    /**
     * 批量新增再保生存金险种明细
     *
     * @param dwsPrpBenefitList 再保生存金险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpBenefit(List<DwsPrpBenefitDTO> dwsPrpBenefitList);

    /**
     * 修改再保生存金险种明细
     *
     * @param dwsPrpBenefitDTO 再保生存金险种明细
     * @return 结果
     */
    public int updateDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO);

    /**
     * 批量删除再保生存金险种明细
     *
     * @param Ids 需要删除的再保生存金险种明细主键集合
     * @return 结果
     */
    public int deleteDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 删除再保生存金险种明细信息
     *
     * @param Id 再保生存金险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpBenefitById(Long Id);

    /**
     * 导入再保生存金险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpBenefit(String companyCode, String companyName, String manageCom, MultipartFile file);

    /**
     * 导出再保生存金险种明细
     *
     * @param response 响应对象
     * @param dwsPrpBenefitQuery 查询条件
     */
    public void exportDwsPrpBenefit(HttpServletResponse response, DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 检查再保生存金险种明细是否存在
     *
     * @param dwsPrpBenefitQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpBenefitExists(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpBenefitPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);

    /**
     * 生成再保生存金险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    public Result generatePrpBenefitData(String startDate, String endDate, Integer reportYear, Integer reportMonth);
}
