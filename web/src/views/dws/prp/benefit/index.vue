<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="交易编码" prop="TransactionNo">
        <el-input
          v-model="queryParams.TransactionNo"
          placeholder="请输入交易编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="保单号" prop="PolicyNo">
        <el-input
          v-model="queryParams.PolicyNo"
          placeholder="请输入个人保单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="团个性质" prop="GPFlag">
        <el-select v-model="queryParams.GPFlag" placeholder="请选择团个性质" clearable>
          <el-option
            v-for="dict in dict.type.prp_gp_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="EventType">
        <el-select v-model="queryParams.EventType" placeholder="请选择业务类型" clearable>
          <el-option
            v-for="dict in dict.type.prp_event_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="保单年度" prop="PolYear">
        <el-input
          v-model="queryParams.PolYear"
          placeholder="请输入保单年度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生存金号" prop="BenefitNo">
        <el-input
          v-model="queryParams.BenefitNo"
          placeholder="请输入生存金号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推送状态" prop="PushStatus">
        <el-select v-model="queryParams.PushStatus" placeholder="请选择推送状态" clearable>
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="DataSource">
        <el-select v-model="queryParams.DataSource" placeholder="请选择数据来源" clearable>
          <el-option
            v-for="dict in dict.type.regulator_report_data_source"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dws:prp:benefit:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dws:prp:benefit:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dws:prp:benefit:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dws:prp:benefit:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['dws:prp:benefit:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-promotion"
          size="mini"
          :disabled="multiple"
          @click="handlePush"
          v-hasPermi="['dws:prp:benefit:push']"
        >推送</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-magic-stick"
          size="mini"
          @click="handleGenerate"
          v-hasPermi="['dws:prp:benefit:generate']"
        >生成数据</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="benefitList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="Id" />
      <el-table-column label="交易编码" align="center" prop="TransactionNo" />
      <el-table-column label="保单号" align="center" prop="PolicyNo" />
      <el-table-column label="险种号" align="center" prop="ProductNo" />
      <el-table-column label="团个性质" align="center" prop="GPFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_gp_flag" :value="scope.row.GPFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="EventType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_event_type" :value="scope.row.EventType"/>
        </template>
      </el-table-column>
      <el-table-column label="保单年度" align="center" prop="PolYear" />
      <el-table-column label="生存金号" align="center" prop="BenefitNo" />
      <el-table-column label="生存金类型" align="center" prop="BenefitType" />
      <el-table-column label="生存金给付日期" align="center" prop="BenefitDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.BenefitDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="被保人姓名" align="center" prop="InsuredName" />
      <el-table-column label="生存金给付金额" align="center" prop="BenefitAmount" />
      <el-table-column label="推送状态" align="center" prop="PushStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.regulator_report_push_status" :value="scope.row.PushStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="DataSource">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.regulator_report_data_source" :value="scope.row.DataSource"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dws:prp:benefit:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dws:prp:benefit:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改再保生存金险种明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="交易编码" prop="TransactionNo">
              <el-input v-model="form.TransactionNo" placeholder="请输入交易编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保险机构代码" prop="CompanyCode">
              <el-input v-model="form.CompanyCode" placeholder="请输入保险机构代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="团体保单号" prop="GrpPolicyNo">
              <el-input v-model="form.GrpPolicyNo" placeholder="请输入团体保单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="团单险种号码" prop="GrpProductNo">
              <el-input v-model="form.GrpProductNo" placeholder="请输入团单保险险种号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="个人保单号" prop="PolicyNo">
              <el-input v-model="form.PolicyNo" placeholder="请输入个人保单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="个单险种号码" prop="ProductNo">
              <el-input v-model="form.ProductNo" placeholder="请输入个单保险险种号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="团个性质" prop="GPFlag">
              <el-select v-model="form.GPFlag" placeholder="请选择团个性质">
                <el-option
                  v-for="dict in dict.type.prp_gp_flag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主附险性质" prop="MainProductFlag">
              <el-select v-model="form.MainProductFlag" placeholder="请选择主附险性质代码">
                <el-option
                  v-for="dict in dict.type.prp_main_product_flag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="主险险种号码" prop="MainProductNo">
              <el-input v-model="form.MainProductNo" placeholder="请输入主险保险险种号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品编码" prop="ProductCode">
              <el-input v-model="form.ProductCode" placeholder="请输入产品编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="责任代码" prop="LiabilityCode">
              <el-input v-model="form.LiabilityCode" placeholder="请输入责任代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任名称" prop="LiabilityName">
              <el-input v-model="form.LiabilityName" placeholder="请输入责任名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务类型" prop="EventType">
              <el-select v-model="form.EventType" placeholder="请选择业务类型">
                <el-option
                  v-for="dict in dict.type.prp_event_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保单年度" prop="PolYear">
              <el-input v-model="form.PolYear" placeholder="请输入保单年度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生存金号" prop="BenefitNo">
              <el-input v-model="form.BenefitNo" placeholder="请输入生存金号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生存金类型" prop="BenefitType">
              <el-input v-model="form.BenefitType" placeholder="请输入生存金类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生存金给付日期" prop="BenefitDate">
              <el-date-picker clearable
                v-model="form.BenefitDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择生存金给付日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生存金给付金额" prop="BenefitAmount">
              <el-input v-model="form.BenefitAmount" placeholder="请输入生存金给付金额" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="importConfig.title" :visible.sync="importConfig.visible" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="importConfig.headers"
        :action="importConfig.url"
        :disabled="importConfig.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="importConfig.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importConfig.isUploading" @click="submitFileForm">确 定</el-button>
        <el-button @click="importConfig.visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBenefit, getBenefit, delBenefit, addBenefit, updateBenefit, pushBenefit, existsBenefit, generateBenefitData } from "@/api/dws/prp/benefit";
import { getToken } from "@/utils/auth";

export default {
  name: "Benefit",
  dicts: ['prp_gp_flag', 'prp_main_product_flag', 'prp_event_type', 'prp_cont_pol_duty_status', 'prp_cert_type', 'prp_reinsur_mode', 'sys_user_sex', 'regulator_report_push_status', 'regulator_report_data_source'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 再保生存金险种明细表格数据
      benefitList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        TransactionNo: null,
        CompanyCode: null,
        PolicyNo: null,
        GPFlag: null,
        EventType: null,
        PolYear: null,
        BenefitNo: null,
        PushStatus: null,
        DataSource: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        TransactionNo: [
          { required: true, message: "交易编码不能为空", trigger: "blur" }
        ],
        CompanyCode: [
          { required: true, message: "保险机构代码不能为空", trigger: "blur" }
        ],
        PolicyNo: [
          { required: true, message: "个人保单号不能为空", trigger: "blur" }
        ],
        ProductNo: [
          { required: true, message: "个单保险险种号码不能为空", trigger: "blur" }
        ],
        GPFlag: [
          { required: true, message: "团个性质不能为空", trigger: "change" }
        ],
        MainProductNo: [
          { required: true, message: "主险保险险种号码不能为空", trigger: "blur" }
        ],
        MainProductFlag: [
          { required: true, message: "主附险性质代码不能为空", trigger: "change" }
        ],
        ProductCode: [
          { required: true, message: "产品编码不能为空", trigger: "blur" }
        ],
        LiabilityCode: [
          { required: true, message: "责任代码不能为空", trigger: "blur" }
        ],
        LiabilityName: [
          { required: true, message: "责任名称不能为空", trigger: "blur" }
        ],
        EventType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ],
        PolYear: [
          { required: true, message: "保单年度不能为空", trigger: "blur" }
        ],
        BenefitNo: [
          { required: true, message: "生存金号不能为空", trigger: "blur" }
        ],
        BenefitType: [
          { required: true, message: "生存金类型不能为空", trigger: "blur" }
        ],
        BenefitDate: [
          { required: true, message: "生存金给付日期不能为空", trigger: "blur" }
        ],
        BenefitReason: [
          { required: true, message: "生存金给付原因不能为空", trigger: "blur" }
        ],
        BenefitAmount: [
          { required: true, message: "生存金给付金额不能为空", trigger: "blur" }
        ],
        Currency: [
          { required: true, message: "货币代码不能为空", trigger: "blur" }
        ],
        AccTransNo: [
          { required: true, message: "所属账单流水号不能为空", trigger: "blur" }
        ],
        DataSource: [
          { required: true, message: "数据来源不能为空", trigger: "change" }
        ],
        PushStatus: [
          { required: true, message: "推送状态不能为空", trigger: "change" }
        ]
      },
      // 导入参数
      importConfig: {
        title: "再保生存金险种明细导入",
        visible: false,
        isUploading: false,
        updateSupport: 0,
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + "/huida-reinsurance/dws/prp/benefit/import"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询再保生存金险种明细列表 */
    getList() {
      this.loading = true;
      listBenefit(this.queryParams).then(response => {
        this.benefitList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        Id: null,
        TransactionNo: null,
        CompanyCode: "000166",
        GrpPolicyNo: null,
        GrpProductNo: null,
        PolicyNo: null,
        ProductNo: null,
        GPFlag: null,
        MainProductNo: null,
        MainProductFlag: null,
        ProductCode: null,
        LiabilityCode: null,
        LiabilityName: null,
        Classification: null,
        EventType: null,
        PolYear: null,
        RenewalTimes: null,
        TermType: null,
        ManageCom: null,
        SignDate: null,
        EffDate: null,
        InvalidDate: null,
        UWConclusion: null,
        PolStatus: null,
        Status: null,
        BasicSumInsured: null,
        RiskAmnt: null,
        Premium: null,
        AccountValue: null,
        FacultativeFlag: null,
        AnonymousFlag: null,
        WaiverFlag: null,
        WaiverPrem: null,
        FinalCashValue: null,
        FinalLiabilityReserve: null,
        InsuredNo: null,
        InsuredName: null,
        InsuredSex: null,
        InsuredCertType: null,
        InsuredCertNo: null,
        OccupationType: null,
        AppntAge: null,
        PreAge: null,
        ProfessionalFee: null,
        SubStandardFee: null,
        EMRate: null,
        ProjectFlag: null,
        InsurePeoples: null,
        SaparateFlag: null,
        ReInsuranceContNo: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ReinsurMode: null,
        BenefitNo: null,
        BenefitType: null,
        BenefitDate: null,
        BenefitReason: null,
        BenefitAmount: null,
        RetentionAmount: null,
        ReinsuranceBenefitAmount: null,
        Currency: null,
        ReComputationsDate: null,
        AccountGetDate: null,
        AccTransNo: null,
        DataSource: 1,
        PushStatus: 0,
        PushDate: null,
        PushBy: null,
        Remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.Id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加再保生存金险种明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const Id = row.Id || this.ids
      getBenefit(Id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改再保生存金险种明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.Id != null) {
            updateBenefit(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBenefit(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = row.Id || this.ids;
      this.$modal.confirm('是否确认删除再保生存金险种明细编号为"' + Ids + '"的数据项？').then(function() {
        return delBenefit(Ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dws/prp/benefit/export', {
        ...this.queryParams
      }, `benefit_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.importConfig.title = "再保生存金险种明细导入";
      this.importConfig.visible = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('dws/prp/benefit/importTemplate', {}, `benefit_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.importConfig.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importConfig.visible = false;
      this.importConfig.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 推送按钮操作 */
    handlePush() {
      const Ids = this.ids;
      this.$modal.confirm('是否确认推送选中的再保生存金险种明细数据？').then(function() {
        return pushBenefit(Ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("推送成功");
      }).catch(() => {});
    },
    /** 生成数据按钮操作 */
    handleGenerate() {
      this.$prompt('请输入生成参数（格式：开始日期,结束日期,报表年份,报表月份）', '生成再保生存金险种明细数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入有效的参数'
      }).then(({ value }) => {
        const params = value.split(',');
        if (params.length !== 4) {
          this.$modal.msgError("参数格式错误，请按照格式输入：开始日期,结束日期,报表年份,报表月份");
          return;
        }
        const [startDate, endDate, reportYear, reportMonth] = params;
        generateBenefitData({
          startDate: startDate.trim(),
          endDate: endDate.trim(),
          reportYear: parseInt(reportYear.trim()),
          reportMonth: parseInt(reportMonth.trim())
        }).then(response => {
          this.$modal.msgSuccess("数据生成成功");
          this.getList();
        });
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
</style>
